package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.SalesSummaryEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 销售汇总
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SalesSummaryDto implements Serializable {
    private static final long serialVersionUID = 999550174627002246L;
    /**
     * key
     */
    @EnumField(enumClazz = SalesSummaryEnum.key.class)
    private String key;
    /**
     * 值1
     */
    private String value1;
}
