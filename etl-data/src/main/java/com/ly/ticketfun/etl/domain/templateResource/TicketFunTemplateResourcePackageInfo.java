package com.ly.ticketfun.etl.domain.templateResource;

import com.ly.ticketfun.etl.domain.templateResource.dto.PackageResourceDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门票玩乐模版套餐资源
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TicketFunTemplateResourcePackageInfo extends PackageResourceDto {
    private static final long serialVersionUID = -2438204871592022185L;
    /**
     * 更新时间
     */
    private Long updateTime;
}
