package com.ly.ticketfun.etl.dataService.tczbactivityresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.localactivity.model.domain.tczbactivityresource.MainResource;
import com.ly.localactivity.model.domain.tczbactivityresource.MainResourceCity;

import java.util.List;

/**
 * <p>
 * 主资源城市表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
public interface IMainResourceCityService extends IDbBaseService<MainResourceCity> {
    /**
     * 保存或更新
     *
     * @param isNewMainResource    是新主要资源
     * @param mainResource         主资源
     * @param mainResourceCityList 主资源城市列表
     */
    void saveOrUpdate(Boolean isNewMainResource, MainResource mainResource, List<MainResourceCity> mainResourceCityList);

    /**
     * 保存或更新
     *
     * @param isNewMainResource    是新主要资源
     * @param mainResource         主资源
     * @param mainResourceCityList 主资源城市列表
     */
    void saveOrUpdate(Boolean isNewMainResource, MainResource mainResource, Integer type, List<MainResourceCity> mainResourceCityList);

    /**
     * 查询有效列表
     *
     * @param mainResourceIds 主要资源id
     * @param type            类型
     * @return {@link List}<{@link MainResourceCity}>
     */
    List<MainResourceCity> queryValid(List<Long> mainResourceIds, Integer type);

}
