package com.ly.ticketfun.etl.dataService.templateresource.impl;


import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.service.impl.EsBaseServiceImpl;
import com.ly.ticketfun.etl.common.enums.es.ESEnum;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourcePackageService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 门票玩乐模版套餐资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
public class TicketFunTemplateResourcePackageInfoServiceImpl extends EsBaseServiceImpl<TicketFunTemplateResourcePackageInfo> implements ITicketFunTemplateResourcePackageService {
    public final static String QUERY_BY_PRODUCT_ID_TEMPLATE_NAME = "QUERY_BY_PRODUCT_ID_TEMPLATE_NAME";

    @PostConstruct
    public void TicketFunTemplateResourcePackageServiceImpl() {
        super.EsBaseServiceImpl(ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_PACKAGE.getIndex(), ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_PACKAGE.getToken());
    }

    /**
     * 按产品id查询
     *
     * @param productId
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByProductId(Long productId) {
        return query(productId, null, null, null);
    }

    /**
     * 按产品id查询
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByProductId(List<Long> productIdList) {
        return query(null, productIdList, null, null);
    }

    /**
     * 按套餐id查询
     *
     * @param packageId 套餐id
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    @Override
    public TicketFunTemplateResourcePackageInfo queryByPackageId(Long packageId) {
        List<TicketFunTemplateResourcePackageInfo> list = query(null, null, packageId, null);
        return list.size() > 0 ? list.get(0) : null;
    }

    /**
     * 按套餐id查询
     *
     * @param packageIdList 套餐id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByPackageId(List<Long> packageIdList) {
        return query(null, null, null, packageIdList);
    }

    /**
     * 查询
     *
     * @param productId 产品id
     * @param packageId 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    public List<TicketFunTemplateResourcePackageInfo> query(Long productId, Long packageId) {
        return query(productId, null, packageId, null);
    }

    private List<TicketFunTemplateResourcePackageInfo> query(Long productId, List<Long> productIdList, Long packageId, List<Long> packageIdList) {
        JSONObject reqJson = new JSONObject();
        if (productId != null || productId > 0) {

        }
        return new ArrayList<>();
    }
}
