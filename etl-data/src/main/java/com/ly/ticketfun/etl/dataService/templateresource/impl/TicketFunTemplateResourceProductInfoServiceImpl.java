package com.ly.ticketfun.etl.dataService.templateresource.impl;


import com.ly.localactivity.framework.service.impl.EsBaseServiceImpl;
import com.ly.ticketfun.etl.common.enums.es.ESEnum;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceProductService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 门票玩乐模版产品资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
public class TicketFunTemplateResourceProductInfoServiceImpl extends EsBaseServiceImpl<TicketFunTemplateResourceProductInfo>  implements ITicketFunTemplateResourceProductService {
    @PostConstruct
    public void TicketFunTemplateResourcePackageServiceImpl() {
        super.EsBaseServiceImpl(ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_PRODUCT.getIndex(), ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_PRODUCT.getToken());
    }

//    /**
//     * 查询
//     *
//     * @param productId 产品id
//     * @return {@link TicketFunTemplateResourceProductInfo}
//     */
//    @Override
//    public TicketFunTemplateResourceProductInfo queryByProductId(Long productId) {
//        List<TicketFunTemplateResourceProductInfo> list = queryByProductId(Arrays.asList(productId));
//        return list.size() > 0 ? list.get(0) : null;
//    }
//
//    /**
//     * 查询列表
//     *
//     * @param productIdList 产品id列表
//     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
//     */
//    @Override
//    public List<TicketFunTemplateResourceProductInfo> queryByProductId(List<Long> productIdList) {
//        JSONObject reqJson = new JSONObject();
//        reqJson.put("productIds", productIdList);
//        EsSearchResult<TicketFunTemplateResourceProductInfo> esSearchResult =  super.query("", reqJson);
//        return esSearchResult != null || esSearchResult.getList() == null ? esSearchResult.getList() : new ArrayList<>();
//    }
}
