package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.RegionEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 位置
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class CountryAreaDto implements Serializable {
    private static final long serialVersionUID = 2712643324115257741L;
    /**
     * 类型
     */
    @EnumField(enumClazz = RegionEnum.Level.class)
    private Integer type;
    /**
     * 位置id
     */
    private Long countryAreaId;
    /**
     * 名称
     */
    private String name;
    /**
     * 父位置
     */
    private CountryAreaDto parentCountryArea;
}
