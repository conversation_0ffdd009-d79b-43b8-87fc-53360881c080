package com.ly.ticketfun.etl.dataService.ticketFunInnerApi.impl;


import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import org.springframework.stereotype.Service;

/**
 * 门票玩乐内部api服务实施
 *
 * <AUTHOR>
 * @date 2025/08/29
 */
@Service
public class TicketFunInnerApiServiceImpl implements ITicketFunInnerApiService {
    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceId 主资源id
     * @param needPackage    需要套餐
     * @return {@link MainResourceAgg}
     */
    public MainResourceAgg queryFunMainResourceAgg(Long mainResourceId, Boolean needPackage) {
        return new MainResourceAgg();
    }
}
