package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBookPassengerInfoEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预订填写出游人问卷
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class BookPassengerQuestionDto implements Serializable {
    private static final long serialVersionUID = -3477825193483546627L;
    /**
     * 问题类型
     */
    @EnumField(enumClazz = ResourceBookPassengerInfoEnum.QuestionType.class)
    private Long questionType;
    /**
     * 出游人必填类型
     */
    @EnumField(enumClazz = ResourceBookPassengerInfoEnum.PassengerRequiredType.class)
    private String passengerRequiredType;
    /**
     * 问题id
     */
    private Long questionId;
    /**
     * 问题code
     */
    @EnumField(enumClazz = ResourceBookPassengerInfoEnum.QuestionInfo.class)
    private Long questionCode;
    /**
     * 问题标题
     */
    private Long questionTitle;
    /**
     * 数据类型
     */
    @EnumField(enumClazz = ResourceBookPassengerInfoEnum.DataType.class)
    private String dataType;
    /**
     * 问题选项
     */
    private List<QuestionItemDto> questionItemList;
    /**
     * 备注
     */
    private String remark;


    /**
     * 问题选项
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @Data
    public static class QuestionItemDto implements Serializable {
        private static final long serialVersionUID = -1459760597757365739L;
        private Long id;
        private String value;
    }
}
