package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 预订联系人信息
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class BookContactInfoDto implements Serializable {
    private static final long serialVersionUID = -6285611918561787864L;
    /**
     * 需要姓名
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer nameRequired;
    /**
     * 需要手机号
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer mobileRequired;
    /**
     * 需要非大陆手机号
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer overseaMobileRequired;
    /**
     * 需要电子邮件
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer emailRequired;
}
