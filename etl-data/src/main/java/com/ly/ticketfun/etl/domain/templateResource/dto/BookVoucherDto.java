package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.BookVoucherEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预订凭证
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class BookVoucherDto implements Serializable {
    private static final long serialVersionUID = -5701787607267782341L;
    /**
     * 是否发送凭证
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer sendVoucher;
    /**
     * 凭证发送方
     */
    @EnumField(enumClazz = BookVoucherEnum.VoucherSender.class)
    private String voucherSender;
    /**
     * 凭证使用方法
     */
    @EnumField(enumClazz = BookVoucherEnum.VoucherUsageMethod.class)
    private List<String> voucherUsageMethod;
}
