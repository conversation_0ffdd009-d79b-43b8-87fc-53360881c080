package com.ly.ticketfun.etl.dataService.ticketFunInnerApi.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 门票玩乐内部api属性
 *
 * <AUTHOR>
 * @date 2025/08/29
 */
@Component
@ConfigurationProperties(prefix = "api.ticketfun.innerapi")
@Data
public class TicketFunInnerApiProperties {
    /**
     * api地址
     */
    private String apiUrl;
}
