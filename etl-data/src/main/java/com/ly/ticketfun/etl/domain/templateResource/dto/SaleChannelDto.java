package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.io.Serializable;

/**
 * 销售渠道
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class SaleChannelDto implements Serializable {
    private static final long serialVersionUID = 8207497206057459009L;
    /**
     * 销售终端Code
     */
    private String salePointCode;
    /**
     * 销售终端名称
     */
    private String salePointName;
    /**
     * 销售渠道id
     */
    private String saleChannelId;
    /**
     * 销售渠道名称
     */
    private String saleChannelName;
    /**
     * 销售RefId
     */
    private String saleRefId;
    /**
     * 产品id
     */
    private String productId;
    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 人民币卖价
     */
    private Decimal salePrice_CNY;
    /**
     * 人民币结算价
     */
    private Decimal netPrice_CNY;
    /**
     * 人民币门市价
     */
    private Decimal marketPrice_CNY;

}
