package com.ly.ticketfun.etl.dataService.tczbactivityresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.domain.tczbactivityresource.MainResource;
import com.ly.localactivity.model.domain.tczbactivityresource.MainResourceCity;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbactivityresource.IMainResourceCityService;
import com.ly.ticketfun.etl.mapper.tczbactivityresource.MainResourceCityMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 主资源城市表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Service
public class MainResourceCityServiceImpl extends DbBaseServiceImpl<MainResourceCityMapper, MainResourceCity> implements IMainResourceCityService {
    @Resource
    private MainResourceCityMapper mainResourceCityMapper;

    /**
     * 保存或更新
     *
     * @param isNewMainResource    是新主要资源
     * @param mainResource         主资源
     * @param mainResourceCityList 主资源城市列表
     */
    @Override
    public void saveOrUpdate(Boolean isNewMainResource, MainResource mainResource, List<MainResourceCity> mainResourceCityList) {
        // 外键赋值
        mainResourceCityList.forEach(obj -> {
            obj.setMainResourceId(mainResource.getId());
            obj.setMainResourceSerialId(mainResource.getSerialId());
        });
        List<MainResourceCity> oldMainResourceCityList = new ArrayList<>();
        if (!isNewMainResource) {
            LambdaQueryWrapper<MainResourceCity> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(MainResourceCity::getMainResourceId, mainResource.getId());
            oldMainResourceCityList = mainResourceCityMapper.selectList(queryWrapper);
        }
        this.saveOrUpdate(oldMainResourceCityList, mainResourceCityList);
    }

    /**
     * 保存或更新
     *
     * @param isNewMainResource    是新主要资源
     * @param mainResource         主资源
     * @param type                 类型
     * @param mainResourceCityList 主要资源城市列表
     */
    @Override
    public void saveOrUpdate(Boolean isNewMainResource, MainResource mainResource, Integer type, List<MainResourceCity> mainResourceCityList) {
        // 外键赋值
        mainResourceCityList.forEach(obj -> {
            obj.setMainResourceId(mainResource.getId());
            obj.setMainResourceSerialId(mainResource.getSerialId());
        });
        List<MainResourceCity> oldMainResourceCityList = new ArrayList<>();
        if (!isNewMainResource) {
            LambdaQueryWrapper<MainResourceCity> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(MainResourceCity::getMainResourceId, mainResource.getId());
            queryWrapper.eq(MainResourceCity::getType, type);
            oldMainResourceCityList = mainResourceCityMapper.selectList(queryWrapper);
        }
        this.saveOrUpdate(oldMainResourceCityList, mainResourceCityList);
    }

    /**
     * 查询有效列表
     *
     * @param mainResourceIds 主要资源id
     * @param type            类型
     * @return {@link List}<{@link MainResourceCity}>
     */
    @Override
    public List<MainResourceCity> queryValid(List<Long> mainResourceIds, Integer type) {
        LambdaQueryWrapper<MainResourceCity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MainResourceCity::getDataFlag, DataFlagEnum.VALID.getValue());
        queryWrapper.in(MainResourceCity::getMainResourceId, mainResourceIds);
        if (type != null) {
            queryWrapper.eq(MainResourceCity::getType, type);
        }
        return mainResourceCityMapper.selectList(queryWrapper);
    }
}
