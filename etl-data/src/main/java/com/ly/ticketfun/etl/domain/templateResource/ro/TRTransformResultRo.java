package com.ly.ticketfun.etl.domain.templateResource.ro;

import com.ly.ticketfun.etl.common.exception.TRTransformException;
import lombok.Data;

import java.text.MessageFormat;

/**
 * 模版资源转换结果
 *
 * <AUTHOR>
 * @date 2025/09/04
 */
@Data
public class TRTransformResultRo<T> {
    /**
     * poi id
     */
    private String poiId;
    /**
     * 产品id
     */
    private String productId;
    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 行程id
     */
    private String journeyId;
    /**
     * 行程id
     */
    private String skuId;
    /**
     * 是否成功
     */
    private Boolean isSuccess;
    /**
     * 错误Code
     */
    private String errCode;
    /**
     * 错误信息
     */
    private String errMsg;
    /**
     * 数据
     */
    private T data;

    public TRTransformResultRo() {
    }

    public TRTransformResultRo(T data) {
        this.isSuccess = true;
        this.data = data;
    }

    public TRTransformResultRo(TRTransformException.ErrorInfo errorInfo) {
        this.setErrorInfo(errorInfo);
    }

    public TRTransformResultRo(TRTransformException.ErrorInfo errorInfo, String... extendList) {
        this.setErrorInfo(errorInfo, extendList);
    }

    public void setErrorInfo(TRTransformException.ErrorInfo errorInfo) {
        this.isSuccess = false;
        this.errCode = errorInfo.getCode();
        this.errMsg = errorInfo.getMsg();
    }

    public void setErrorInfo(TRTransformException.ErrorInfo errorInfo, String... extendList) {
        this.isSuccess = false;
        this.errCode = errorInfo.getCode();
        this.errMsg = MessageFormat.format(errorInfo.getMsg(), extendList);
    }

}
