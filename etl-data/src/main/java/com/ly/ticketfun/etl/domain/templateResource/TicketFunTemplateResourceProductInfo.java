package com.ly.ticketfun.etl.domain.templateResource;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.localactivity.framework.annotation.model.DataFlagField;
import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.domain.templateResource.dto.ProductResourceDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门票玩乐模版产品资源
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ticketfun-templateresource-productinfo")
public class TicketFunTemplateResourceProductInfo extends ProductResourceDto {
    private static final long serialVersionUID = -8218253733144317432L;
    /**
     * id
     */
    @TableId("id")
    private String id;
    /**
     * 是否在售
     */
    @EnumField(enumClazz = WhetherEnum.class)
    @DataFlagField
    private Integer isOnSale;
    /**
     * 更新时间
     */
    private Long updateTime;
}
