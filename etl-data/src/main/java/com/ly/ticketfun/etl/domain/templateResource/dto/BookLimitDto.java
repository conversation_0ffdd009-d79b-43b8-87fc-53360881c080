package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 预订限制
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class BookLimitDto implements Serializable {
    private static final long serialVersionUID = 2012351388039735773L;
    /**
     * 需要提前
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer needAdvance;
    /**
     * 提前订天数
     */
    private Integer advanceBookDays;
    /**
     * 提前订最后小时(例：7:00前)
     */
    private Integer advanceBookHour;
    /**
     * 提前订最后分钟(例：7:00前)
     */
    private Integer advanceBookMinute;
    /**
     * 提前订描述
     */
    private String advanceBookDescription;
    /**
     * 预订生效分钟数
     */
    private BigDecimal bookEffectMinutes;
    /**
     * 支付超时分钟数
     */
    private Integer payTimeoutMinutes;
    /**
     * 距离当前最大预订天数
     */
    private Integer bookDaysFromCurrent;
    /**
     * 预订最小份数
     */
    private Integer bookMinQuantity;
    /**
     * 预订最大份数
     */
    private Integer bookMaxQuantity;
    /**
     * 预订最小天数（WIFI独有）
     */
    private Integer bookMinDay;
    /**
     * 预订最大天数（WIFI独有）
     */
    private Integer bookMaxDay;
    /**
     * 购买配额列表
     */
    private List<BookQuotaLimitDto> bookQuotaLimitList;

}
