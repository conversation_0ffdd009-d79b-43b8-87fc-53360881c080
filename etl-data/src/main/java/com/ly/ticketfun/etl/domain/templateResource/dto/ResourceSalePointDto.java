package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 资源销售卖点
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ResourceSalePointDto implements Serializable {
    private static final long serialVersionUID = 2306829165395578872L;
    /**
     * 亮点名称
     */
    private String name;
    /**
     * 亮点内容
     */
    private String content;
    /**
     * 亮点图片
     */
    private List<String> imageUrlList;
    /**
     * 排序
     */
    private Integer sort;
}
