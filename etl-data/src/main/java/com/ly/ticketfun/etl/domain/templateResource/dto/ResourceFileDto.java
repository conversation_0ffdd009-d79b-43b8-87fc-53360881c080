package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceImageEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 资源文件
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
    public class ResourceFileDto implements Serializable {
    private static final long serialVersionUID = 6127516235831133694L;
    /**
     * 文件类型
     */
    @EnumField(enumClazz = ResourceImageEnum.FileType.class)
    private String fileType;
    /**
     * 头图类型
     */
    @EnumField(enumClazz = ResourceImageEnum.HeadImageType.class)
    private String headImageType;
    /**
     * 文件id
     */
    private String fileId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件url
     */
    private String fileUrl;
    /**
     * 压缩url
     */
    private String fileCompressUrl;
    /**
     * 文件封面url
     */
    private String fileCoverUrl;

    /**
     * 封面名称
     */
    private String fileCoverName;

    /**
     * 排序
     */
    private Integer sort;
    /**
     * 文件标签列表
     */
        private List<FileLabelDto> fileLabelList;

    /**
     * 文件标签
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @Data
    public static class FileLabelDto implements Serializable {
        private static final long serialVersionUID = 7495840421466299620L;
        /**
         * code
         */
        private String code;
        /**
         * 名称
         */
        private String name;
        /**
         * 备注
         */
        private String remark;
        /**
         * 排序
         */
        private Integer sort;
        /**
         * 子列表
         */
        private List<FileLabelDto> childList;
    }
}
