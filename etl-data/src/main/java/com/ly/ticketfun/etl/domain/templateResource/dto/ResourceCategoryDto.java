package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 资源品类
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ResourceCategoryDto implements Serializable {
    private static final long serialVersionUID = -5193578973590967263L;
    /**
     * 品类id
     */
    private Long categoryId;
    /**
     * 品类名称
     */
    private String categoryName;
    /**
     * 子品类信息列表
     */
    private List<ResourceCategoryDto> subcategoryList;
}
