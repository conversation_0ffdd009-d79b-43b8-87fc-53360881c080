package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.resource.TimeCompareRuleEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.RefundPolicyEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 退款政策
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class RefundPolicyDto implements Serializable {
    private static final long serialVersionUID = -8608385907421284134L;
    /**
     * 退款类型
     */
    @EnumField(enumClazz = RefundPolicyEnum.RefundType.class)
    private String refundType;
    /**
     * 时间限制类型
     */
    @EnumField(enumClazz = RefundPolicyEnum.TimeLimitType.class)
    private String timeLimitType;
    /**
     * 支持部分退款
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer partialRefund;
    /**
     * 支持过期退款
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer overdueRefund;
    /**
     * 附加说明
     */
    private String additionalNote;
    /**
     * 明细列表
     */
    private List<RefundPolicyItemDto> itemList;


    @Data
    public static class RefundPolicyItemDto implements Serializable {
        private static final long serialVersionUID = -7960417081042191891L;
        /**
         * 比较日期类型
         */
        @EnumField(enumClazz = RefundPolicyEnum.CompareTimeType.class)
        private String compareTimeType;
        /**
         * 指定日期
         */
        private String specifyDate;
        /**
         * 比较天数
         * -X表示前X天
         */
        private Integer compareDays;
        /**
         * 比较小时
         */
        private Integer compareHour;
        /**
         * 比较分钟
         */
        private Integer compareMinute;
        /**
         * 比较时间前后
         */
        @EnumField(enumClazz = TimeCompareRuleEnum.class)
        private String compareTimeAfter;
        /**
         * 有损数值类型
         */
        @EnumField(enumClazz = RefundPolicyEnum.CostType.class)
        private String costType;
        /**
         * 有损数值
         */
        private String costValue;
        /**
         * 有损数值货币
         */
        private String costCurrency;
        /**
         * 描述
         */
        private String description;
    }
}
