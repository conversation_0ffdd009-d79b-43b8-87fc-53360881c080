package com.ly.ticketfun.etl.dataService.ticketFunInnerApi;

import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;

/**
 * 门票玩乐内部api服务
 *
 * <AUTHOR>
 * @date 2025/08/29
 */
public interface ITicketFunInnerApiService {
    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceId 主资源id
     * @param needPackage    需要套餐
     * @return {@link MainResourceAgg}
     */
    MainResourceAgg queryFunMainResourceAgg(Long mainResourceId, Boolean needPackage);
}
