package com.ly.ticketfun.etl.dataService.templateresource.impl;


import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.model.es.EsSearchResult;
import com.ly.localactivity.framework.service.impl.EsBaseServiceImpl;
import com.ly.ticketfun.etl.common.enums.es.ESEnum;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceSkuService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 门票玩乐模版SKU资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
public class TicketFunTemplateResourceSkuInfoServiceImpl extends EsBaseServiceImpl<TicketFunTemplateResourceSkuInfo> implements ITicketFunTemplateResourceSkuService {
    @PostConstruct
    public void TicketFunTemplateResourcePackageServiceImpl() {
        super.EsBaseServiceImpl(ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_SKU.getIndex(), ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_SKU.getToken());
    }

    /**
     * 按产品id查询
     *
     * @param productId 产品id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByProductId(Long productId) {
        return query(productId, null, null , null);
    }

    /**
     * 按产品id查询
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByProductId(List<Long> productIdList) {
        return query(null, productIdList, null , null);
    }

    /**
     * 按套餐id查询
     *
     * @param packageId 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByPackageId(Long packageId) {
        return query(null, null, packageId , null);
    }

    /**
     * 按套餐id查询
     *
     * @param packageIdList 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByPackageId(List<Long> packageIdList) {
        return query(null, null, null, packageIdList);
    }

    private List<TicketFunTemplateResourceSkuInfo> query(Long productId, List<Long> productIdList, Long packageId, List<Long> packageIdList) {
        JSONObject reqJson = new JSONObject();
        if (productId != null || productId > 0) {

        }
        return new ArrayList<>();
    }
}
