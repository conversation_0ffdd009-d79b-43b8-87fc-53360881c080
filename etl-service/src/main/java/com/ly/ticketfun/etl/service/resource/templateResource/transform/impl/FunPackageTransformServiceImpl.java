package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.dataService.tczbactivityresource.IMainResourceCityService;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 玩乐套餐转换模版资源服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Service
public class FunPackageTransformServiceImpl extends TRTransformBaseServiceImpl implements ITRTransformService {

    @Resource
    private IMainResourceCityService mainResourceCityService;
    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    /**
     * 支持
     *
     * @param resourceType   资源类型
     * @param changeCategory 变更类别
     * @return {@link Boolean}
     */
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return resourceType.equals(TemplateResourceConstant.ResourceType.FUN)
                && (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PACKAGE_CHANGE)
                || changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.TRAVEL_JOURNEY_CHANGE));
    }

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类别
     * @return {@link List}<{@link TRTransformResultRo}>
     */
    @Override
    public List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> transformDataList(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        // -------------- 入参校验 --------------
//        if (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PACKAGE_CHANGE)
//                && ((changeRo.getProductId() == null || changeRo.getProductId() < 1) || (changeRo.getPackageId() == null || changeRo.getPackageId() < 1))) {
//            return new ArrayList<>(Arrays.asList(new TRTransformResultRo(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "产品Id或套餐Id为空")));
//        }
//        if (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.TRAVEL_JOURNEY_CHANGE)
//                && ((changeRo.getProductId() == null || changeRo.getProductId() < 1) || (changeRo.getJourneyId() == null || changeRo.getJourneyId() < 1))) {
//            return new ArrayList<>(Arrays.asList(new TRTransformResultRo(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "产品Id或套餐Id为空")));
//        }
        // -------------- 数据查询 --------------

        // -------------- 数据校验 --------------

        // -------------- 数据匹配 --------------

        // -------------- 数据返回 --------------
        return null;
    }

}
