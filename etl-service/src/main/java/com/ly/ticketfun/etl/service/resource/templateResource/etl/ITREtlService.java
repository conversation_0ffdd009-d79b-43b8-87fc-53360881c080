package com.ly.ticketfun.etl.service.resource.templateResource.etl;

import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;

/**
 * 模版资源转换服务
 *
 * <AUTHOR>
 * @date 2025/09/04
 */
public interface ITREtlService {
    /**
     * 门票资源变更
     *
     * @param changeRo 更改ro
     * @return {@link Boolean}
     */
    Boolean ticketResourceChange(TicketFunResourceChangeRo changeRo);
    /**
     * 玩乐资源变更
     *
     * @param changeRo 更改ro
     * @return {@link Boolean}
     */
    Boolean funResourceChange(TicketFunResourceChangeRo changeRo);
}
