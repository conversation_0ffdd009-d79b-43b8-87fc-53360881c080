package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 玩乐Sku价格转换模版资源服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Service
public class FunSkuPriceTransformServiceImpl extends TRTransformBaseServiceImpl implements ITRTransformService {


    /**
     * 支持
     *
     * @param resourceType   资源类型
     * @param changeCategory 变更类别
     * @return {@link Boolean}
     */
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return resourceType.equals(TemplateResourceConstant.ResourceType.FUN)
                && changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.SKU_PRICE_CHANGE);
    }

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类别
     * @return {@link List}<{@link TRTransformResultRo}>
     */
    public List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> transformDataList(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        // -------------- 入参校验 --------------

        // -------------- 数据查询 --------------

        // -------------- 数据校验 --------------

        // -------------- 数据匹配 --------------

        // -------------- 数据返回 --------------
        return null;
    }

}
