package com.ly.ticketfun.etl.common.enums.templateResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品逻辑扩展枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface LogicExtendEnum {
    /**
     * key
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum key {
        /**
         * 取件方式
         */
        TAKE_TYPE("takeType", "取件方式"),
        /**
         * 自取城市
         */
        SELF_PICKUP_CITY("selfPickupCity", "自取城市"),
        /**
         * 发货城市
         */
        DISPATCH_CITY("dispatchCity", "发货城市"),
        /**
         * 待机时长
         */
        STANDBY_DURATION("standbyDuration", "待机时长"),
        ;
        private final String value;
        private final String name;
    }
}
