package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.common.FileTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源图片枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface ResourceImageEnum {

    /**
     * 图片类型
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum FileType {
        IMAGE_INTRODUCE("IMAGE_INTRODUCE", "介绍图片", FileTypeEnum.IMAGE.getValue()),
        VIDEO_INTRODUCE("VIDEO_INTRODUCE", "介绍视频", FileTypeEnum.VIDEO.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 头图类型
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum HeadImageType {
        NONE("NONE", "不是头图", com.ly.localactivity.model.enums.tczbactivityresource.ResourceImageEnum.IsMainImage.MAIN_IMAGE.getValue()),
        LIST_HEAD_IMAGE("LIST_HEAD_IMAGE", "列表头图", com.ly.localactivity.model.enums.tczbactivityresource.ResourceImageEnum.IsMainImage.MAIN_IMAGE.getValue()),
        ONLY_LIST_HEAD_IMAGE("ONLY_LIST_HEAD_IMAGE", "仅列表头图", com.ly.localactivity.model.enums.tczbactivityresource.ResourceImageEnum.IsMainImage.ONLY_MAIN_IMAGE.getValue()),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;

    }
}
