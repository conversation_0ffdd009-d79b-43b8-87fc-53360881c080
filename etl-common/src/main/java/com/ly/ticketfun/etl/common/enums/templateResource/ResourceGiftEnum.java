package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceGiftEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源赠品枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface ResourceGiftEnum {

    /**
     * 有效时间类型
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum ValidityTimeType {
        SPECIFIED("SPECIFIED", "指定有效日期", MainResourceGiftEnum.ValidityTimeType.SPECIFIED.getValue()),
        NOT_SPECIFIED("NOT_SPECIFIED", "无需指定", MainResourceGiftEnum.ValidityTimeType.NOT_SPECIFIED.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }
}
