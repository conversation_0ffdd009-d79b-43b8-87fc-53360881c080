package com.ly.ticketfun.etl.common.enums.templateResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源预订出游人信息枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface ResourceBookPassengerInfoEnum {
    /**
     * 问题类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum QuestionType {
        BASIC("BASIC", "基础问题"),
        CREDENTIALS("CREDENTIALS", "证件类问题"),
        ADDITIONAL("ADDITIONAL", "附加问题"),
        ;
        private final String value;
        private final String name;
    }

    /**
     * 出游人必填类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum PassengerRequiredType {
        NO_REQUIRED("NO_REQUIRED", "无需"),
        REQUIRED_ONE("REQUIRED_ONE", "需要一个"),
        REQUIRED_ALL("REQUIRED_ALL", "需要所有"),
        ;
        private final String value;
        private final String name;
    }

    /**
     * 数据类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum DataType {
        DATETIME("DATETIME", "日期及时间", "格式：yyyy-MM-dd HH:mm:ss"),
        DATE("DATE", "日期", "格式：yyyy-MM-dd"),
        TIME("TIME", "时间", "格式：HH:mm"),
        TEXT("TEXT", "时间", ""),
        NUMBER("NUMBER", "数字", ""),
        BOOLEAN("BOOLEAN", "布尔", ""),
        SINGLE_ENUM("ENUM", "单选", ""),
        MULTIPLE_ENUM("ENUM", "多选", ""),
        ADDRESS("ADDRESS", "地址", ""),
        ;
        private final String value;
        private final String name;
        private String description;
    }

    /**
     * 问题
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum QuestionInfo {
        B_CN_NAME("B_CN_NAME", "中文姓名", QuestionType.BASIC),
        B_EN_FIRST_NAME("B_EN_FIRST_NAME", "英语名", QuestionType.BASIC),
        B_EN_LAST_NAME("B_EN_LAST_NAME", "英语姓", QuestionType.BASIC),
        B_TEL_PHONE("B_TEL_PHONE", "联系电话", QuestionType.BASIC),
        B_TEL_PHONE_AREA_CODE("B_TEL_PHONE_AREA_CODE", "电话区号", QuestionType.BASIC),
        B_HEIGHT("B_HEIGHT", "身高（cm）", QuestionType.BASIC),
        B_GENDER("B_GENDER", "性别", QuestionType.BASIC),
        B_Birthday("B_BIRTHDAY", "出生日期", QuestionType.BASIC),
        B_COUNTRY_OR_DISTRICT("B_COUNTRY_OR_DISTRICT", "国家/地区", QuestionType.BASIC),
        B_WEIGHT("B_WEIGHT", "体重（kg）", QuestionType.BASIC),
        B_SHOE_SIZE_EU("B_SHOE_SIZE_EU", "鞋码（欧码）", QuestionType.BASIC),
        B_CERTIFICATE_VALIDITY_PERIOD("B_CERTIFICATE_VALIDITY_PERIOD", "证件有效期", QuestionType.BASIC),
        B_GLASSES_DEGREE("B_GLASSES_DEGREE", "眼镜度数（度）", QuestionType.BASIC),
        B_HM_ENDORSEMENT_TYPE("B_HM_ENDORSEMENT_TYPE", "港澳签注类型", QuestionType.BASIC),
        B_PROVINCE_OF_ISSUE("B_PROVINCE_OF_ISSUE", "签发地（省份）", QuestionType.BASIC),
        B_PROVINCE_OF_BIRTH("B_PROVINCE_OF_Birth", "出生地（省份）", QuestionType.BASIC),
        B_CLIENT_ID("B_CLIENT_ID", "出行人ID", QuestionType.BASIC),

        C_ID_CARD("C_ID_CARD", "身份证", QuestionType.CREDENTIALS),
        C_STUDENT_CARD("C_STUDENT_CARD", "学生证", QuestionType.CREDENTIALS),
        C_HM_PASS("C_HM_PASS", "港澳通行证", QuestionType.CREDENTIALS),
        C_TW_PASS("C_TW_PASS", "台湾通行证", QuestionType.CREDENTIALS),
        C_PASSPORT("C_PASSPORT", "身份证", QuestionType.CREDENTIALS),
        C_DRIVE_CARD("C_DRIVE_CARD", "驾驶证", QuestionType.CREDENTIALS),
        C_TW_ID_CARD("C_TW_ID_CARD", "台胞证", QuestionType.CREDENTIALS),
        C_BACK_HOME_CARD("C_BACK_HOME_CARD", "回乡证", QuestionType.CREDENTIALS),
        C_PERMANENT_RESIDENCE_CARD_FOREIGNERS("C_PERMANENT_RESIDENCE_CARD_FOREIGNERS", "外国人永久居留身份证", QuestionType.CREDENTIALS),
        C_MILITARY_CARD("C_MILITARY_CARD", "军官证", QuestionType.CREDENTIALS),

        A_ON_LOCATION("A_ON_LOCATION", "上车点", QuestionType.ADDITIONAL),
        ;
        private final String value;
        private final String name;
        private QuestionType questionType;
    }
}
