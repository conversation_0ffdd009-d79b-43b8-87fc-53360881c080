package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceBookConfigEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预订凭证枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface BookVoucherEnum {
    /**
     * 凭证发送方
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum VoucherSender {
        TC_SEND("TC_SEND", "同程发送", MainResourceBookConfigEnum.SendVoucherType.TC_SEND.getValue()),
        SUPPLIER_SEND("SUPPLIER_SEND", "供应商发送", MainResourceBookConfigEnum.SendVoucherType.SUPPLIER_SEND.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 凭证使用方法
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum VoucherUsageMethod {

        ;
        private final String value;
        private final String name;
    }
}
