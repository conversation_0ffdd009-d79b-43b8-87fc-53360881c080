package com.ly.ticketfun.etl.common.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生肖枚举
 * <AUTHOR>
 * @date 2025/09/05
 */
@AllArgsConstructor
@Getter
public enum ZodiacEnum {
    RAT("鼠", "Rat", 1),
    OX("牛", "Ox", 2),
    TIGER("虎", "<PERSON>", 3),
    RABBIT("兔", "Rabbit", 4),
    DRAGON("龙", "Dragon", 5),
    SNAKE("蛇", "Snake", 6),
    HORSE("马", "Horse", 7),
    GOAT("羊", "Goat", 8),
    MONKEY("猴", "Monkey", 9),
    ROOSTER("鸡", "Rooster", 10),
    DOG("狗", "Dog", 11),
    PIG("猪", "Pig", 12);

    private final String chineseName;
    private final String englishName;
    private final int order;

    public static ZodiacEnum fromYear(int year) {
        int index = (year - 4) % 12;
        if (index < 0) {
            index += 12;
        }
        return values()[index];
    }
}
