package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.ResourceNoticeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface BookModeEnum {
    /**
     * 价格模式
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum PriceMode {
        SPECIFY_SINGLE_USAGE_DATE("SPECIFY_SINGLE_USAGE_DATE", "指定单使用日期"),
        SPECIFY_MULTIPLE_USAGE_DATE("SPECIFY_SINGLE_USAGE_DATE", "指定多使用日期"),
        NOT_SPECIFY_USAGE_DATE("NOT_SPECIFY_USAGE_DATE", "不指定使用日期（期票）"),
        ;
        private final String value;
        private final String name;
    }

    /**
     * 使用时限类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum UsagePeriodType {
        ON_THAT_DAY("ON_THAT_DAY", "当日", null),
        FROM_SPECIFY_DATE_ONWARD("FROM_SPECIFY_DATE_ONWARD", "指定日期起", null),
        FROM_ORDER_DATE_ONWARD("FROM_ORDER_DATE_ONWARD", "下单日期起", ResourceNoticeEnum.ValidityTimeType.CREATE_DATE.getValue().toString()),
        SPECIFY_VALIDITY_PERIOD("SPECIFY_VALIDITY_PERIOD", "指定有效期", ResourceNoticeEnum.ValidityTimeType.SPECIFIED_VALIDITY.getValue().toString()),
        INDEFINITELY("INDEFINITELY", "无限期", ResourceNoticeEnum.ValidityTimeType.UNLIMITED.getValue().toString()),
        ;
        private final String value;
        private final String name;
        private final String funValue;
    }

    /**
     * 政策模式枚举
     */
    @AllArgsConstructor
    @Getter
    enum PolicyMode{
        Regular_Ticket("Regular_Ticket","普通票(指定日)"),
        Kill_Ticket("Kill_Ticket","秒杀票"),
        Original_Price_Ticket("Original_Price_Ticket","原价票(后台录单)"),
        Free_Tickets("Free_Tickets","0元票 "),
        Session_Tickets("Session_Tickets","场次票"),
        Additional_Products("Additional_Products","附加产品"),
        Open_Product("Open_Product","开放产品"),
        Combined_Ticket("Combined_Ticket","联票")
        ;
        private final String value;
        private final String name;
    }

}
