package com.ly.ticketfun.etl.common.enums.es;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * es枚举
 *
 * <AUTHOR>
 * @date 2025/09/08
 */
public interface ESEnum {
    @AllArgsConstructor
    @Getter
    enum Index {
        TICKET_FUN_TEMPLATE_RESOURCE_PRODUCT("ticketfun-templateresource-productinfo", "门票玩乐模版资源库产品信息", ""),
        TICKET_FUN_TEMPLATE_RESOURCE_PACKAGE("ticketfun-templateresource-packageinfo", "门票玩乐模版资源库套餐信息", ""),
        TICKET_FUN_TEMPLATE_RESOURCE_SKU("ticketfun-templateresource-skuresourceinfo", "门票玩乐模版资源库sku信息", ""),
        ;
        private final String index;
        private final String name;
        private final String token;
    }
}
