package com.ly.ticketfun.etl.common.enums.templateResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品枚举
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
public interface ProductResourceEnum {

    /**
     * 销售状态
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum SaleStatus {
        CAN_SALE("CAN_SALE", "售卖中"),
        CAN_NOT_SALE("CAN_NOT_SALE", "不可售"),
        ;
        private final String value;
        private final String name;
    }
}
