package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceCrowdEnum;
import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分类（人群）信息枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface ResourceBandInfoEnum {
    /**
     * 分类（人群）Code
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum BandCode {
        ADULT("ADULT", "成人", MainResourceCrowdEnum.CrowdCode.ADULT.getValue()),
        CHILD("CHILD", "儿童", MainResourceCrowdEnum.CrowdCode.CHILD.getValue()),
        SENIOR("SENIOR", "老人", MainResourceCrowdEnum.CrowdCode.SENIOR.getValue()),
        INFANT("INFANT", "婴儿", MainResourceCrowdEnum.CrowdCode.INFANT.getValue()),
        OTHER("OTHER", "其他", MainResourceCrowdEnum.CrowdCode.OTHER.getValue()),
        STANDARD("STANDARD", "标准价", MainResourceCrowdEnum.CrowdCode.STANDARD.getValue()),
        ;
        private final String value;
        private final String name;
        private final String funValue;
    }

    /**
     * 限制条件类型
     *
     * <AUTHOR>
     * @date 2024/12/17
     */
    @AllArgsConstructor
    @Getter
    enum RangeType {
        AGE("AGE", "年龄"),
        HEIGHT("HEIGHT", "身高");
        private final String value;
        private final String name;
    }

    /**
     * 生日维度
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Getter
    @AllArgsConstructor
    enum BirthdayDimension {
        DAY("DAY", "日", MainResourceCrowdEnum.CalcCondition1.BIRTHDAY.getValue()),
        MONTH("MONTH", "月", null),
        YEAR("YEAR", "年份", MainResourceCrowdEnum.CalcCondition1.YEAR.getValue());
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 年龄计算比较维度
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @AllArgsConstructor
    @Getter
    enum AgeCalcCompareDimension {
        USAGE_DATE(TimeNodeEnum.USAGE_DATE, MainResourceCrowdEnum.CalcCondition2.TRAVEL_DATE.getValue()),
        CREATE_DATE(TimeNodeEnum.CREATE_DATE, MainResourceCrowdEnum.CalcCondition2.BOOKING_DATE.getValue()),
        ;
        AgeCalcCompareDimension(TimeNodeEnum usageDate, Integer funValue) {
            this.value = usageDate.getValue();
            this.name = usageDate.getName();
            this.funValue = funValue;
        }

        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 身份类型
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @AllArgsConstructor
    @Getter
    enum IdentityType {
        STUDENT("STUDENT", "学生", "包含研究生、硕士、博士"),
        STUDENT_EXCLUDE_POSTGRADUATE("STUDENT_EXCLUDE_POSTGRADUATE", "学生", "不包含研究生、硕士、博士"),
        ;

        private final String value;
        private final String name;
        private final String desc;
    }
}
