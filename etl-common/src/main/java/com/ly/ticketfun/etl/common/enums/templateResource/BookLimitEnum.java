package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.ResourceNoticeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预订限制枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface BookLimitEnum {
    @AllArgsConstructor
    @Getter
    enum UsagePeriodType {
        ON_THAT_DAY("ON_THAT_DAY", "当日", null),
        FROM_SPECIFY_DATE_ONWARD("FROM_SPECIFY_DATE_ONWARD", "指定日期起", null),
        FROM_ORDER_DATE_ONWARD("FROM_ORDER_DATE_ONWARD", "下单日期起", ResourceNoticeEnum.ValidityTimeType.CREATE_DATE.getValue().toString()),
        SPECIFY_VALIDITY_PERIOD("SPECIFY_VALIDITY_PERIOD", "指定有效期", ResourceNoticeEnum.ValidityTimeType.SPECIFIED_VALIDITY.getValue().toString()),
        INDEFINITELY("INDEFINITELY", "无限期", ResourceNoticeEnum.ValidityTimeType.UNLIMITED.getValue().toString()),
        ;
        private final String value;
        private final String name;
        private final String funValue;
    }
}
