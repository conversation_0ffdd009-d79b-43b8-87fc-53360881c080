package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.CancelChangeRuleEnum;
import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款政策枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface RefundPolicyEnum {
    /**
     * 退款类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum RefundType {
        FREE("FREE", "未使用随时退", CancelChangeRuleEnum.RuleType.ANY_TIME_WHEN_UN_USE.getValue()),
        LOSS("LOSS", "有损退", CancelChangeRuleEnum.RuleType.BASE_RULE.getValue()),
        REFUSE("REFUSE", "不可退", CancelChangeRuleEnum.RuleType.CAN_NOT.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 日期限制类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum TimeLimitType {
        NO_TIME_LIMIT("NO_TIME_LIMIT", "无时间限制", CancelChangeRuleEnum.DateType.NO_TIME_LIMIT.getValue()),
        TIME_LIMIT("TIME_LIMIT", "有时间限制", CancelChangeRuleEnum.DateType.TIME_LIMIT.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 比较时间类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum CompareTimeType {
        TRAVEL_DATE(TimeNodeEnum.USAGE_DATE),
        SPECIFY_DATE(TimeNodeEnum.SPECIFY_DATE),
        ;

        CompareTimeType(TimeNodeEnum usageDate) {
            this.value = usageDate.getValue();
            this.name = usageDate.getName();
        }
        private final String value;
        private final String name;
    }

    /**
     * 有损数值类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum CostType {
        PERCENTAGE("PERCENTAGE", "百分比", CancelChangeRuleEnum.FeeType.PERCENTAGE.getValue()),
        CONSTANT("CONSTANT", "资源固定值", CancelChangeRuleEnum.FeeType.CONSTANT.getValue()),
        UNIT_CONSTANT("UNIT_CONSTANT", "每份（资源）固定值", CancelChangeRuleEnum.FeeType.UNIT_CONSTANT.getValue()),
        TEXT_DESCRIPTION("TEXT_DESCRIPTION", "文字描述", CancelChangeRuleEnum.FeeType.TEXT_DESCRIPTION.getValue()),
        BASE_SUPPLIER_RETURN("BASE_SUPPLIER_RETURN", "基于供应商返回", CancelChangeRuleEnum.FeeType.BASE_SUPPLIER_RETURN.getValue()),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;

    }
}
