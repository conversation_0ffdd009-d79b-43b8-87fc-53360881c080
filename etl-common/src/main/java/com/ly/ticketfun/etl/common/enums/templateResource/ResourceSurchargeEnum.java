package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.SubResourceSurchargeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 资源附加费枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface ResourceSurchargeEnum {
    /**
     * 费用类型
     */
    @AllArgsConstructor
    @Getter
    enum FeeType {
        DEPOSIT("DEPOSIT", "押金", SubResourceSurchargeEnum.FeeType.SECURITY_DEPOSIT.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;

    }

    /**
     * 收取方式
     */
    @AllArgsConstructor
    @Getter
    enum CollectType {
        CONSTANT("CONSTANT", "资源固定值", SubResourceSurchargeEnum.CollectType.BY_SINGLE.getValue()),
        UNIT_CONSTANT("UNIT_CONSTANT", "每份（资源）固定值", SubResourceSurchargeEnum.CollectType.BY_QUANTITY.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 收取途径
     */
    @AllArgsConstructor
    @Getter
    enum CollectWay {
        FREE_PAYMENT("FREE_PAYMENT", "免支付", SubResourceSurchargeEnum.CollectWay.FREE_PAYMENT.getValue()),
        ONLINE_PAYMENT("ONLINE_PAYMENT", "在线支付", SubResourceSurchargeEnum.CollectWay.ONLINE_PAYMENT.getValue()),
        ON_SITE_PAYMENT("ON_SITE_PAYMENT", "现场支付", SubResourceSurchargeEnum.CollectWay.ON_SITE_PAYMENT.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 承担方
     */
    @AllArgsConstructor
    @Getter
    enum CostBear {
        TC_BEAR("TC_BEAR", "同程承担", SubResourceSurchargeEnum.CostBear.TC_BEAR.getValue()),
        NON_TC_BEAR("NON_TC_BEAR", "非同程承担", SubResourceSurchargeEnum.CostBear.NON_TC_BEAR.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 退款类型
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum RefundType {
        CAN_REFUND("CAN_REFUND", "可退", WhetherEnum.YES.getValue()),
        CAN_NOT_REFUND("CAN_NOT_REFUND", "不可退", WhetherEnum.NO.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }
}
