package com.ly.ticketfun.etl.common.enums.mq;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class MQEnum {
    // -------------- 门票资源变更通知 --------------
    public final static String TURBO_TOPIC_TICKET_RESOURCE_CHANGE = "ticketFun_ticket_resource_change_topic";
    public final static String TURBO_GROUP_TICKET_RESOURCE_CHANGE = "ticketFun_ticket_resource_change_group";
    // -------------- 玩乐资源变更通知 --------------
    public final static String TURBO_TOPIC_FUN_RESOURCE_CHANGE = "ticketFun_fun_resource_change_topic";
    public final static String TURBO_GROUP_FUN_RESOURCE_CHANGE = "ticketFun_fun_resource_change_group";

    /**
     * 门票玩乐资源变更分类
     *
     * <AUTHOR>
     * @date 2025/09/04
     */
    @AllArgsConstructor
    @Getter
    public enum TicketFunResourceChangeCategory {
        PRODUCT_CHANGE("PRODUCT_CHANGE", "产品变更"),
        PACKAGE_CHANGE("PACKAGE_CHANGE", "套餐变更"),
        TRAVEL_JOURNEY_CHANGE("TRAVEL_JOURNEY_CHANGE", "行程变更"),
        SKU_PRICE_CHANGE("SKU_PRICE_CHANGE", "SKU价格变更"),
        ;

        private final String value;
        private final String name;
    }
}
