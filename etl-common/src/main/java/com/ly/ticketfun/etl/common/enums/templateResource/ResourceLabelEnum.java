package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.ResourceTagConfigEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源标签枚举
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
public interface ResourceLabelEnum {

    /**
     * 类型
     *
     * <AUTHOR>
     * @date 2025/09/01
     */
    @AllArgsConstructor
    @Getter
    enum Type {
        SUPPLIER_EXE("SUPPLIER_EXE", "商家标签", ResourceTagConfigEnum.Type.MANUAL_OPERATION.getValue()),
        SYSTEM_CALC("SYSTEM_CALC", "系统计算标签", ResourceTagConfigEnum.Type.MANUAL_OPERATION.getValue()),
        OPERATION_EXE("OPERATION_EXE", "运营标签", ResourceTagConfigEnum.Type.MANUAL_OPERATION.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }
}
