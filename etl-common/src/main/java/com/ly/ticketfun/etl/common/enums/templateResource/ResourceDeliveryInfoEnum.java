package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.SubResourceTakeReturnEnum;
import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源配送信息枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface ResourceDeliveryInfoEnum {
    /**
     * 配送类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum DeliveryType {
        NO_NEED("NO_NEED", "无需配送", SubResourceTakeReturnEnum.TakeType.NO_NEED_TAKE.getValue()),
        TC_DELIVERY("TC_DELIVERY", "同程配送", SubResourceTakeReturnEnum.TakeType.TC_DELIVERY.getValue()),
        SUPPLIER_DELIVERY("SUPPLIER_DELIVERY", "供应商配送", SubResourceTakeReturnEnum.TakeType.SUPPLIER_DELIVERY.getValue()),
        SELF_PICKUP("SELF_PICKUP", "自取", SubResourceTakeReturnEnum.TakeType.SELF_PICKUP.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 配送时间类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum DeliveryTimeType {
        BEFORE_USAGE_DATE(TimeNodeEnum.BEFORE_USAGE_DATE),
        AFTER_ORDER_CONFIRM(TimeNodeEnum.AFTER_ORDER_CONFIRM),
        ;

        DeliveryTimeType(TimeNodeEnum usageDate) {
            this.value = usageDate.getValue();
            this.name = usageDate.getName();
        }

        private final String value;
        private final String name;
    }
}
