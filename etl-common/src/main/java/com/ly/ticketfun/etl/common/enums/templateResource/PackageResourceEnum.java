package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 套餐资源枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface PackageResourceEnum {
    @AllArgsConstructor
    @Getter
    enum InvoiceProvider {
        PLATFORM("PLATFORM", "平台提供", MainResourceEnum.InvoiceMode.PLATFORM_PROVIDES_INVOICE.getValue()),
        SUPPLIER("SUPPLIER", "供应商提供", MainResourceEnum.InvoiceMode.SUPPLIER_PROVIDES_INVOICE.getValue()),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;
    }
}
