package com.ly.ticketfun.etl.service.resource.templateResource.transfer;

import com.ly.localactivity.model.domain.tczbactivityresource.MainResource;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transfer.impl.FunProductTransferServiceImpl;
import com.ly.ticketfun.etl.templateresource.TemplateResourceEtlApplication;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * FunProductTransferServiceImplTest
 *
 * <AUTHOR>
 * @date 2025/9/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TemplateResourceEtlApplication.class)
public class FunProductTransferServiceImplTest {
    @Resource
    private FunProductTransferServiceImpl transferService;

    private MainResourceAgg mainResourceAgg;

    @Before
    public void before() {
        mainResourceAgg = new MainResourceAgg();
        mainResourceAgg.setMainResource(new MainResource());
    }

    @Test
    public void testMatchData() {
        transferService.matchData(mainResourceAgg);
    }
}