package com.ly.ticketfun.etl.templateresource.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RequestMapping("/")
@RestController
public class MonitorController {

    @GetMapping("/monitor")
    public String doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        return "Ok";
    }
}
