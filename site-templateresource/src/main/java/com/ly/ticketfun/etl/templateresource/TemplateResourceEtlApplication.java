package com.ly.ticketfun.etl.templateresource;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableAsync
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.ly.ticketfun.etl", "com.ly.localactivity"})
public class TemplateResourceEtlApplication {
    public static void main(String[] args) {
        SpringApplication.run(TemplateResourceEtlApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  门票玩乐虚拟商品ETL服务站启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}
