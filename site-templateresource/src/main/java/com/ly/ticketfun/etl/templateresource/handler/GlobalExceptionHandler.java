//package com.ly.ticketfun.api.inner.handler;
//
//
//import cn.hutool.http.HttpStatus;
//import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
//import com.ly.localactivity.framework.exception.ApiException;
//import com.ly.localactivity.framework.utils.log.LogUtils;
//import com.ly.ticketfun.api.domain.ctrip.openService.common.ApiResult;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.validation.BindException;
//import org.springframework.web.HttpRequestMethodNotSupportedException;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//
//import javax.servlet.http.HttpServletRequest;
//import java.nio.file.AccessDeniedException;
//
///**
// * 全局异常处理器
// *
// * <AUTHOR>
// */
//@RestControllerAdvice
//public class GlobalExceptionHandler {
//
//    /**
//     * 权限校验异常
//     */
//    @ExceptionHandler(AccessDeniedException.class)
//    public ApiResult handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
//        String requestURI = request.getRequestURI();
//        LogUtils.error(LogOperateTypeEnum.OTHER, e);
//        return ApiResult.error(HttpStatus.HTTP_FORBIDDEN,"没有权限，请联系管理员授权");
//    }
//
//    /**
//     * 请求方式不支持
//     */
//    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
//    public ApiResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
//                                                          HttpServletRequest request) {
//        String requestURI = request.getRequestURI();
//        LogUtils.error(LogOperateTypeEnum.OTHER, e);
//        return ApiResult.error(e.getMessage());
//    }
//
//    /**
//     * 业务异常
//     */
//    @ExceptionHandler(ApiException.class)
//    public ApiResult handleServiceException(ApiException e, HttpServletRequest request) {
//        LogUtils.warn(LogOperateTypeEnum.OTHER, e.getMessage());
//        String code = e.getCode();
//        return StringUtils.isNotEmpty(code) ? ApiResult.error(code, e.getMessage()) : ApiResult.error(e.getMessage());
//    }
//
//    /**
//     * 拦截未知的运行时异常
//     */
//    @ExceptionHandler(RuntimeException.class)
//    public ApiResult handleRuntimeException(RuntimeException e, HttpServletRequest request) {
//        String requestURI = request.getRequestURI();
//        LogUtils.error(LogOperateTypeEnum.OTHER, e);
//        return ApiResult.error(e.getMessage());
//    }
//
//    /**
//     * 系统异常
//     */
//    @ExceptionHandler(Exception.class)
//    public ApiResult handleException(Exception e, HttpServletRequest request) {
//        String requestURI = request.getRequestURI();
//        LogUtils.error(LogOperateTypeEnum.OTHER, e);
//        return ApiResult.error(e.getMessage());
//    }
//
//    /**
//     * 自定义验证异常
//     */
//    @ExceptionHandler(BindException.class)
//    public ApiResult handleBindException(BindException e) {
//        LogUtils.error(LogOperateTypeEnum.OTHER, e);
//        String message = e.getAllErrors().get(0).getDefaultMessage();
//        return ApiResult.error(message);
//    }
//
//    /**
//     * 自定义验证异常
//     */
//    @ExceptionHandler(MethodArgumentNotValidException.class)
//    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
//        LogUtils.error(LogOperateTypeEnum.OTHER, e);
//        String message = e.getBindingResult().getFieldError().getDefaultMessage();
//        return ApiResult.error(message);
//    }
//
//}
