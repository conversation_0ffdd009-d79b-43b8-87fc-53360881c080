package com.ly.ticketfun.etl.templateresource.listener;

import com.alibaba.fastjson2.JSON;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.localactivity.framework.annotation.CheckListLog;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.mq.LAMqListener;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.etl.ITREtlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Service
public class MqMessageListener {
    @Resource
    private ITREtlService trTransferService;

    /**
     * 监听门票资源变更
     */
    @LAMqListener(topic = MQEnum.TURBO_TOPIC_TICKET_RESOURCE_CHANGE, consumerGroup = MQEnum.TURBO_GROUP_TICKET_RESOURCE_CHANGE)
    @CheckListLog
    public void listenTicketResourceChange(MessageExt message) {
        try {
            String msg = formatTurboMsg(message);
            TicketFunResourceChangeRo changeRo = JSON.parseObject(msg, TicketFunResourceChangeRo.class);
            trTransferService.ticketResourceChange(changeRo);
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.SUBSCRIBE, ex);
        }
    }

    /**
     * 监听玩乐资源变更
     */
    @LAMqListener(topic = MQEnum.TURBO_TOPIC_FUN_RESOURCE_CHANGE, consumerGroup = MQEnum.TURBO_GROUP_FUN_RESOURCE_CHANGE)
    @CheckListLog
    public void listenFunResourceChange(MessageExt message) {
        try {
            String msg = formatTurboMsg(message);
            TicketFunResourceChangeRo changeRo = JSON.parseObject(msg, TicketFunResourceChangeRo.class);
            trTransferService.funResourceChange(changeRo);
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.SUBSCRIBE, ex);
        }
    }

    /**
     * 格式化消息
     *
     * @param message
     * @return
     */
    private static String formatTurboMsg(MessageExt message) {
        return new String(message.getBody(), StandardCharsets.UTF_8);
    }
}
