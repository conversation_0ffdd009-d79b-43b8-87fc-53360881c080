local-activity:
  appName: tcscenery.java.ticketfun.templateresource.etl
  fastjson-converter:
    enabled: false
  config:
    dal:
      className:
        mysql: com.mysql.jdbc.Driver
        sqlServer: com.microsoft.sqlserver.jdbc.SQLServerDriver
      TCZBActivityResource:
        enabled: false
        name: TCZBActivityResource
        mapperLocation: classpath*:mapper/tczbactivityresource/**/*.xml
        basePackages: com.ly.ticketfun.etl.mapper.tczbactivityresource
        transactionManager: true
    redis:
      enabled: true
      groupName: ${config.redis.groupName}
      prefix: ${config.redis.prefix}
      env: ${config.redis.env}
    turbomq:
      enabled: true
      namesrvAddr: ${config.turbomq.namesrvAddr}
      producerGroups:
        - localActivity_mainResourceTagClean_group
    languageTran:
      enabled: true
      projectId: project_f26108a200a14ed5aa06335bb86c1fa9


