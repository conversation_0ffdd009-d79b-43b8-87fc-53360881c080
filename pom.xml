<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.14</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.ly.ticketfun</groupId>
    <artifactId>etl.region</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>etl-service</module>
        <module>etl-data</module>
        <module>etl-common</module>
        <module>site-templateresource</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <application.version>1.0-SNAPSHOT</application.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <springboot.version>2.5.14</springboot.version>
        <localactivity-framework.version>1.0.63-SNAPSHOT</localactivity-framework.version>
        <localactivity-model.version>0.0.23-tr-SNAPSHOT</localactivity-model.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.localactivity</groupId>
                <artifactId>localactivity-framework</artifactId>
                <version>${localactivity-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.localactivity</groupId>
                <artifactId>localactivity-model</artifactId>
                <version>${localactivity-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.ticketfun</groupId>
                <artifactId>etl-service</artifactId>
                <version>${application.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.ticketfun</groupId>
                <artifactId>etl-data</artifactId>
                <version>${application.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.ticketfun</groupId>
                <artifactId>etl-common</artifactId>
                <version>${application.version}</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>5.2.1.RELEASE</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>tc_service</id>
            <name>tongcheng public repository</name>
            <url>https://nexus.17usoft.com/repository/mvn-all/</url>
        </repository>
    </repositories>
</project>
